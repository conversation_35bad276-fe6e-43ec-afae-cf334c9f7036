package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Property;
import com.pmw790.power.schema.BindingPattern;

import java.util.Map;

/**
 * Context class for room-level binding operations to reduce parameter passing
 * This class encapsulates all the parameters needed for room-level binding operations
 */
public class RoomBindingContext {
    // Core elements
    private final BindingPattern pattern;
    private final Class roomBlock;
    private final Project project;
    private final Property ownerBlock;
    private final DiagramPresentationElement diagram;
    private final RoomDiagramContext context;

    // Constraint and property maps
    private final Map<String, Map<String, Property>> constraintPortsMap;
    private final Map<String, Property> valueProperties;

    // Consumer-specific field
    private final boolean isConsumer;

    /**
     * Constructor for constraint-to-property pattern processing in room context
     */
    public RoomBindingContext(
            BindingPattern pattern,
            Class roomBlock,
            Project project,
            Property ownerBlock,
            Map<String, Map<String, Property>> constraintPortsMap,
            Map<String, Property> valueProperties,
            DiagramPresentationElement diagram,
            RoomDiagramContext context,
            boolean isConsumer) {

        this.pattern = pattern;
        this.roomBlock = roomBlock;
        this.project = project;
        this.ownerBlock = ownerBlock;
        this.constraintPortsMap = constraintPortsMap;
        this.valueProperties = valueProperties;
        this.diagram = diagram;
        this.context = context;
        this.isConsumer = isConsumer;
    }

    // Getters
    public BindingPattern getPattern() {
        return pattern;
    }

    public Class getRoomBlock() {
        return roomBlock;
    }

    public Project getProject() {
        return project;
    }

    public Property getOwnerBlock() {
        return ownerBlock;
    }

    public Map<String, Map<String, Property>> getConstraintPortsMap() {
        return constraintPortsMap;
    }

    public Map<String, Property> getValueProperties() {
        return valueProperties;
    }

    public DiagramPresentationElement getDiagram() {
        return diagram;
    }

    public RoomDiagramContext getContext() {
        return context;
    }

    public boolean isConsumer() {
        return isConsumer;
    }
}
